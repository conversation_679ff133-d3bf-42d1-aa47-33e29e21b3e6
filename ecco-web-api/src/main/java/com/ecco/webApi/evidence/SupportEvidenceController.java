package com.ecco.webApi.evidence;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.EvidenceSupportWorkSummary;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.EvidenceSupportWorkRepository;
import com.ecco.dom.*;
import com.ecco.dom.commands.DeleteCommand;
import com.ecco.dto.DelegateResponse;
import com.ecco.dto.FlagsDefinition;
import com.ecco.dto.EvidenceLegacyReviewDto;
import com.ecco.evidence.repositories.CalendarEventSnapshotRepository;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.ServiceRecipientRepositoryCustom;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.service.EvidenceService;
import com.ecco.service.TaskDefinitionService;
import com.ecco.service.ReviewService;
import com.ecco.webApi.contacts.FlagsDefinitionToViewModel;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.controllers.NotFoundException;
import com.ecco.webApi.controllers.ReportUnsecuredDelegator;
import com.ecco.webApi.upload.UploadedFileResourceAssembler;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.jpa.JPQLQuery;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.data.domain.Slice;
import org.springframework.data.querydsl.QPageRequest;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ecco.config.service.SettingsService.Evidence.PageSizeHistory;
import static com.ecco.dom.EvidenceGroup.CHECKLIST;
import static com.ecco.dom.EvidenceGroup.NEEDS;
import static com.ecco.security.SecurityUtil.getUser;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
public class SupportEvidenceController extends BaseWebApiController {

    private final ReportUnsecuredDelegator reportUnsecuredDelegator;
    private final SupportWorkSummaryToViewModel supportWorkSummaryToViewModel;
    private final ReviewChoicesToViewModel reviewChoicesToViewModel = new ReviewChoicesToViewModel();
    private final ReviewService reviewService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final EvidenceFlagToViewModel flagToViewModel = new EvidenceFlagToViewModel();
    private final TaskDefinitionService taskDefinitionService;
    private final ReferralRepository referralRepository;
    private final ServiceRecipientRepositoryCustom serviceRecipientRepository;
    private final CalendarEventSnapshotRepository calendarEventSnapshotRepository;
    private final CalendarEventSnapshotToDto calendarEventSnapshotToDto;
    private final EvidenceService evidenceService;
    private final FlagsDefinitionToViewModel flagsDefinitionToViewModel;

    @Autowired
    public SupportEvidenceController(EvidenceSupportWorkRepository supportWorkRepository,
                                     ReportUnsecuredDelegator reportUnsecuredDelegator,
                                     ReviewService reviewService, ApplicationProperties appProps,
                                     ReferralRepository referralRepository,
                                     ServiceRecipientRepository serviceRecipientRepository,
                                     CalendarEventSnapshotRepository calendarEventSnapshotRepository,
                                     DemandScheduleRepository demandScheduleRepository,
                                     TaskDefinitionService taskDefinitionService,
                                     EvidenceService evidenceService,
                                     ListDefinitionRepository listDefinitionRepository) {
        this.supportWorkRepository = supportWorkRepository;
        this.reportUnsecuredDelegator = reportUnsecuredDelegator;
        this.reviewService = reviewService;
        UploadedFileResourceAssembler assembler = new UploadedFileResourceAssembler(appProps.getApplicationRootPath());
        supportWorkSummaryToViewModel = new SupportWorkSummaryToViewModel(assembler);
        this.referralRepository = referralRepository;
        this.calendarEventSnapshotRepository = calendarEventSnapshotRepository;
        this.taskDefinitionService = taskDefinitionService;
        this.serviceRecipientRepository = serviceRecipientRepository;
        this.evidenceService = evidenceService;
        this.flagsDefinitionToViewModel = new FlagsDefinitionToViewModel(listDefinitionRepository);
        calendarEventSnapshotToDto = new CalendarEventSnapshotToDto(serviceRecipientRepository, demandScheduleRepository);
    }

    /** Used for offline sync to give plain list so encryption is a list of encrypted objects
     * TODO: See if wen can get secure proxy to understand Slice ?
     * TODO: Should we go straight to first page only here, so offline sync is good?  Could check Ajax stats on NewRelic
     */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupKey}/all/")
    public List<EvidenceSupportWorkViewModel> findAllByServiceRecipientId(@PathVariable int serviceRecipientId,
                                                                          @NonNull @PathVariable String evidenceGroupKey,
                                                                          WebRequest request) {
        return findBySiblingServiceRecipientIds(serviceRecipientId, evidenceGroupKey, null, false, false, false, null, request).getContent();
    }

    /**
     * TODO performance issue - this loads all history of all serviceRecipients
     *  - used in testing
     *  - used in production with hactOnly smart steps (which restricts to statusChangeOnly and specific actionDefIds) - we should create specific method
     *  - used in production with changeOnly - we should be using the smart step difference report
     */
    @Deprecated
    @GetJson("/service-recipients/evidence/{evidenceGroupKey}/")
    public Stream<List<EvidenceSupportWorkViewModel>> findAllByServiceRecipientIds(
            @NonNull @PathVariable String evidenceGroupKey,
            @RequestParam(name = "serviceRecipientId") List<Integer> srIds,
            @RequestParam(name = "hactOnly", defaultValue = "false") Boolean hactOnly,
            @RequestParam(name = "changesOnly", defaultValue = "false") Boolean changesOnly) {

        EvidenceGroup evidenceGroup = taskDefinitionService.findGroupFromGroupName(evidenceGroupKey);
        return srIds.stream().map(srId ->
                supportWorkRepository.findAllSupportWorkSummaryWithActions(Collections.singleton(srId), evidenceGroup, null, false, hactOnly, changesOnly)
                        .map(supportWorkSummaryToViewModel).getContent());
    }

    /**
     * Used for SupportRadarChart and support history and ReportDataSourceFactory - ordered by newest first: workDate DESC, created DESC.
     * @param hactOnly Gets only the smart steps that were ever mapped to HACT, and even then only when made relevant (Only the trigger date is needed).
     * @param serviceIds serviceIds to load other serviceRecipientIds from the same client
     */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupKey}/")
    public Slice<EvidenceSupportWorkViewModel> findByServiceRecipientIdRequest(@PathVariable int serviceRecipientId,
                                                                               @NonNull @PathVariable String evidenceGroupKey,
                                                                               @RequestParam(name = "page", required = false) Integer page,
                                                                               @RequestParam(name = "hactOnly", defaultValue = "false") Boolean hactOnly,
                                                                               @RequestParam(name = "statusChangeOnly", defaultValue = "false") Boolean statusChangeOnly,
                                                                               @RequestParam(name = "serviceIds", required = false) List<Integer> serviceIds,
                                                                               WebRequest request) {
        return findBySiblingServiceRecipientIds(serviceRecipientId, evidenceGroupKey, page, false, hactOnly, statusChangeOnly, serviceIds, request);
    }

    /** Unused - now using EvidenceAttachmentController
     * NB offlineRepository.syncInboundDataToOfflineDatabase syncs all work, not specifically attachments, so this method isn't called
     */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence/needs/{evidenceGroupKey}/attachments/")
    public Slice<EvidenceSupportWorkViewModel> findAttachmentsByServiceRecipientId(@PathVariable int serviceRecipientId,
                                                                                   @NonNull @PathVariable String evidenceGroupKey,
                                                                                   @RequestParam(name = "page", required = false) Integer page,
                                                                                   @RequestParam(name = "serviceIds", required = false) List<Integer> serviceIds,
                                                                                   WebRequest request) {
        return findBySiblingServiceRecipientIds(serviceRecipientId, evidenceGroupKey, page, true, false, false, serviceIds, request);
    }

    public Slice<EvidenceSupportWorkViewModel> findBySiblingServiceRecipientIds(int serviceRecipientId, String evidenceGroupKey, Integer page,
                                                                                boolean findAttachmentsOnly, boolean hactOnly, boolean statusChangeOnly,
                                                                                List<Integer> serviceIds,
                                                                                WebRequest request) {
        if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) {
            return null;
        }

        // NB potentially, the cache above may return without new referrals
        Set<Integer> srIds = SupportEvidenceController.getSrIdWithSiblings(referralRepository, serviceRecipientRepository, serviceRecipientId, serviceIds);

        QPageRequest pr = page == null ? null : settingsService.settingFor(PageSizeHistory).asQPageRequest(page);
        EvidenceGroup evidenceGroup = taskDefinitionService.findGroupFromGroupName(evidenceGroupKey);
        final Slice<EvidenceSupportWorkSummary> supportWork = supportWorkRepository.findAllSupportWorkSummaryWithActions(srIds, evidenceGroup, pr, findAttachmentsOnly, hactOnly, statusChangeOnly);
        return supportWork.map(supportWorkSummaryToViewModel);
    }

    /**
     * Get all the siblings srIds (including this srId to be sure its included)
     */
    protected static Set<Integer> getSrIdWithSiblings(ReferralRepository referralRepository, ServiceRecipientRepositoryCustom serviceRecipientRepository,
                                                      int serviceRecipientId, List<Integer> serviceIds) {
        if (serviceIds == null || serviceIds.size() == 0) {
            return Collections.singleton(serviceRecipientId);
        }
        var clientId = referralRepository.getClientIdByServiceRecipientId(serviceRecipientId);
        assert clientId != null;
        var srIds = referralRepository.findAllServiceRecipientIdsFromClientId(clientId);
        var serviceIdsLong = serviceIds.stream().map(Integer::longValue).collect(Collectors.toList());
        var withSiblings = serviceRecipientRepository.findAllLimitedToServiceIds(srIds, serviceIdsLong)
                .stream().map(sr -> sr.serviceRecipientId).collect(Collectors.toList());
        withSiblings.add(serviceRecipientId);
        return new HashSet<>(withSiblings);
    }

    /** Used for Tasks to open one support history item for deletion etc
     * see TaskCard.tsx. Also used for CareVisitOverview.tsx and 'showVisit' (also via rota)
     * to know the status of a visit (for start/stop/continue).
     * NB evidenceGroupKey is probably not required when supplying the uuid, which we are
     *  and we probably should just have /support/uuid - to differentiate support/threat/questionnaire
     *  because they are the different view models returned
     *  the whole list is TaskEvidenceType (ts) or TaskDefinition.Type (java) - but this doesn't differentiate evidence tasks
     */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupKey}/uuid/{uuid}/")
    public EvidenceSupportWorkViewModel findOneSupportWorkByWorkUuid(@PathVariable int serviceRecipientId,
                                                                     @NonNull @PathVariable String evidenceGroupKey,
                                                                     @NonNull @PathVariable UUID uuid) {
        EvidenceGroup evidenceGroup = taskDefinitionService.findGroupFromGroupName(evidenceGroupKey);
        var dto = supportWorkRepository.findOneSupportWorkSummaryWithActions(serviceRecipientId, evidenceGroup, uuid)
                .map(supportWorkSummaryToViewModel)
                .orElseThrow(() -> new NotFoundException("already deleted"));
        // provide the current snapshot status of the work, for CareVisitOverview.tsx and "showVisit" (also called from the rota)
        // NB it doesn't seem that the eventStatus is used in the EvidenceView
        // it is additional information only, available whilst the snapshot does
        if (dto != null) {
            // if the visit has 'started' there will be a work item associated during the snapshot
            dto.eventSnapshot = calendarEventSnapshotRepository.findOneByWorkUuid(uuid)
                    .map(calendarEventSnapshotToDto)
                    .orElse(null);
        }
        return dto;
    }

    /** Used for referral overview to show risk management required */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence/{evidenceGroupKey}/riskManagementOutstanding/")
    public Stream<EvidenceSupportWorkViewModel> findRiskManagementOutstandingByServiceRecipientId(
            @PathVariable int serviceRecipientId,
            @NonNull @PathVariable String evidenceGroupKey,
            WebRequest request) {

        if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) {
            return null;
        }

        EvidenceGroup evidenceGroup = taskDefinitionService.findGroupFromGroupName(evidenceGroupKey);
        final List<EvidenceSupportWorkSummary> supportWork =
                supportWorkRepository.findAllSupportWorkSummaryByServiceRecipientIdAndRiskManagementOutstanding(serviceRecipientId, evidenceGroup);
        return supportWork.stream().map(supportWorkSummaryToViewModel);
    }

    /**
     * EvidenceSnapshot contains the latest SmartSteps for the recipient.
     *
     * This currently is equivalent to findLatestByServiceRecipientId
     * except wrapped in an array containing the one element. The result is
     * placed in the offline database.
     *
     * This method will be useful if all evidenceGroupId's are supported
     * - eg not just needs/support/review, but also 'manager notes' and 'staff notes' etc
     * because there will be snapshots for each of these evidenceGroupId's.
     * NB The latest actions include any from the repo (so 'manager notes' and
     * 'staff notes'), but fortunately any non-needs pages aren't configured with
     * any smart steps anyway.
     *
     * This is a clone of QuestionnaireEvidenceController.findLatestAnswersByServiceRecipientIdAndEvidenceGroupKey.
     * It was ready to be used for showing the appropriate data when editing historical work, for when the handlers can
     * process the data correctly but currently the handlers always load the latest and apply commands on that.
     * It is used to show the latest smart steps when loading evidence pages because /latest defers here, but now we can
     * also retrieve snapshots at a point in time for smart step reporting (as per questionnaire movement reports).
     * This gives rise to uses in other areas:
     * TODO radar charts at specific points in time? (rather than statusChangesOnly on the whole history)
     * TODO hact data? (rather than statusChangesOnly on the whole history)
     * TODO editing historical work? (see DEV-729, but the handlers need to work out propagating information through history first)
     * NB The latestActions are unordered, but may be hard to order within a query as it involves outcome -> actionGroup -> actions.
     */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/snapshots/")
    public SupportSmartStepsSnapshotViewModel findLatestByServiceRecipientIdAndTimestamp(
            @PathVariable int serviceRecipientId,
            @PathVariable String evidenceGroup,
            @RequestParam(required=false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) DateTime workDate,
            @RequestParam(required=false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) DateTime created,
            WebRequest request) {

        if (isUnmodifiedAndSetLastModified(serviceRecipientId, request)) {
            return null;
        }

        EvidenceGroup group = taskDefinitionService.findGroupFromGroupName(evidenceGroup);
        Assert.state(group.equals(NEEDS) || group.equals(CHECKLIST), "group is not of the allowed type");

        DateTime snapshotWorkDate = workDate == null ? DateTime.now(DateTimeZone.UTC) : workDate;
        DateTime snapshotCreated = created == null ? DateTime.now(DateTimeZone.UTC) : created;
        JPQLQuery<EvidenceSupportAction> query = reportUnsecuredDelegator.supportActionInstanceSnapshotQuery(serviceRecipientId, Collections.singletonList(group), snapshotWorkDate, snapshotCreated, true);
        List<SupportSmartStepsSnapshotViewModel> actions = reportUnsecuredDelegator.getSupportActionInstanceSnapshotViewModels(
                QEvidenceSupportAction.evidenceSupportAction, group, query);

        if (actions.size() > 0) {
            return actions.get(0);
        } else {
            // provide a return even if no snapshots
            SupportSmartStepsSnapshotViewModel result = new SupportSmartStepsSnapshotViewModel();
            result.evidenceGroupKey = group;
            result.latestActions = Collections.emptyList();
            result.serviceRecipientId = serviceRecipientId;
            result.parentId = referralRepository.getReferralIdByServiceRecipientId(serviceRecipientId); // TODO: This will be NULL for buildings - which is fine
            return result;
        }
    }

    /**
     * When working online we just want to the latest for the page we are on
     * TODO we should supply evidenceGroup as an argument, but need to differentiate from how threat being handled differently??
     */
    @GetJson("/service-recipients/{serviceRecipientId}/evidence/{evidenceGroup}/snapshots/latest/")
    public SupportSmartStepsSnapshotViewModel findLatestByServiceRecipientId(
            @PathVariable int serviceRecipientId,
            @PathVariable String evidenceGroup,
            WebRequest request) {
        var latest = findLatestByServiceRecipientIdAndTimestamp(serviceRecipientId, evidenceGroup, null, null, request);
        if (latest == null) {
            return null;
        }

        // add the latest flags...
        DateTime snapshotWorkDate = DateTime.now(DateTimeZone.UTC);
        DateTime snapshotCreated = DateTime.now(DateTimeZone.UTC);
        EvidenceGroup group = taskDefinitionService.findGroupFromGroupName(evidenceGroup);
        JPQLQuery<EvidenceSupportFlag> flagsQuery = reportUnsecuredDelegator.flagSnapshotQuery(serviceRecipientId, Collections.singletonList(group), snapshotWorkDate, snapshotCreated, true);
        List<EvidenceSupportFlag> flags = flagsQuery.fetch();
        latest.latestFlags = flags.stream()
                .map(flagToViewModel).collect(Collectors.toList());

        return latest;
    }

    @GetJson("/service-recipients/{serviceRecipientId}/evidence/needs/reviewChoices/")
    public ReviewChoicesViewModel findReviewChoices(@PathVariable int serviceRecipientId) {
        EvidenceLegacyReviewDto reviewDto = reviewService.getReviewDto(serviceRecipientId);
        return reviewChoicesToViewModel.apply(reviewDto);
    }

    @PostJson("/service-recipients/{serviceRecipientId}/evidence/needs/review/{reviewId}/complete/")
    @ResponseStatus(HttpStatus.OK)
    public Result setReviewComplete(@PathVariable int serviceRecipientId, @PathVariable long reviewId) {
        Review r = reviewService.getReview(reviewId);
        Assert.state(serviceRecipientId == r.getServiceRecipient().getId());
        // TODO perhaps some other logic about completeness
        reviewService.setComplete(reviewId);
        return new Result("updated");
    }


    @PostJson("/service-recipients/{serviceRecipientId}/evidence/needs/review/")
    public Map<String, Long> createReview(@PathVariable int serviceRecipientId,
            @NonNull @RequestBody String body) throws IOException {

        JsonNode node = objectMapper.readTree(body).get("reviewDate");
        String reviewDateTxt = node.asText();
        LocalDate reviewDate = new LocalDate(reviewDateTxt);
        Review r = new Review(serviceRecipientId);
        r.setStartDate(reviewDate.toDateTimeAtStartOfDay()); // localdate saved into UTC field
        reviewService.setReview(r);
        return Collections.singletonMap("id", r.getId());
    }

    @PostJson("/service-recipients/{serviceRecipientId}/evidence/needs/review/schedule/")
    @ResponseStatus(HttpStatus.OK)
    public Result customReviewDate(@PathVariable int serviceRecipientId,
            @NonNull @RequestBody String body) throws IOException {

        JsonNode node = objectMapper.readTree(body).get("customReviewDate");
        String reviewDateTxt = node.asText();
        DateTime reviewDate = new LocalDate(reviewDateTxt).toDateTimeAtStartOfDay();

        reviewService.setCustomReviewDate(serviceRecipientId, reviewDate);
        return new Result("updated");
    }

    @PostJson("/service-recipients/{serviceRecipientId}/evidence/needs/review/schedule/automated/")
    @ResponseStatus(HttpStatus.OK)
    public Result scheduleReviewDates(@PathVariable int serviceRecipientId,
            @NonNull @RequestBody String body) throws IOException {

        JsonNode node = objectMapper.readTree(body).get("reviewSchedule");
        String schedule = node.asText();
        reviewService.setReviewDates(serviceRecipientId, schedule);
        return new Result("updated");
    }

    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @RequestMapping(value = "/evidence/needs/{workUuid}", method = RequestMethod.DELETE, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.ACCEPTED)
    public Result delete(
            @PathVariable UUID workUuid,
            @RequestBody EvidenceSupportWorkViewModel evidence,
            @NonNull Authentication authentication) {
        Assert.isNull(evidence.id, "No id should be set in request body on DELETE");

        EvidenceSupportWork existing = supportWorkRepository.findById(workUuid).orElseThrow();

        Assert.state(existing.getServiceRecipientId().equals(evidence.serviceRecipientId),
                "The supplied serviceRecipientId did not match");

        supportWorkRepository.delete(existing);

        long userId = getUser(authentication).getId();
        // TODO: Change to proper command based with command sent from client and able to be done offline
        // TODO: In body record workUuid, workDate of evidence that got deleted
        DeleteCommand deleteCommand = new DeleteCommand(UUID.randomUUID(), DateTime.now().toInstant(),
                userId, "{}", // For effin Oracle
                evidence.serviceRecipientId, "needs", null);
        commandRepository.save(deleteCommand);
        return new Result("deleted");
    }

    @GetJson("/clients/flags/fromExternal/{externalSourceName}/{externalRef}")
    public List<FlagViewModel> getFromExternal(
            @PathVariable String externalSourceName,
            @PathVariable String externalRef) {
        DelegateResponse<FlagsDefinition> results = evidenceService.queryClientFlags(externalSourceName, externalRef);
        if (results == null) {
            throw new RuntimeException("No data source with name: " + externalSourceName + " is configured in ECCO.");
        }

        if (results.getStatusCode() >= 400) {
            throw new RuntimeException(results.getStatusText());
        }

        FlagsDefinition result = results.getPayload();
        return flagsDefinitionToViewModel.apply(result);
    }

}
