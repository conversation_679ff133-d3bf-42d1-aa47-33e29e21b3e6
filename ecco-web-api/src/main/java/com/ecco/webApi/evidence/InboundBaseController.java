package com.ecco.webApi.evidence;

import com.ecco.dao.AgencyRepository;
import com.ecco.dom.Agency;
import com.ecco.dom.Individual;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.web.WebSlice;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.webApi.clients.InboundReferralResource;
import com.ecco.webApi.featureConfig.SessionDataController;
import com.ecco.webApi.forms.FormDefinitionController;
import com.ecco.webApi.forms.FormDefinitionViewModel;
import com.ecco.webApi.serviceConfig.ServiceController;
import com.ecco.webApi.serviceConfig.ServiceTypeController;
import com.ecco.webApi.serviceConfig.ServiceViewModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.WebRequest;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.UUID;

import static org.springframework.util.StringUtils.hasText;

@RestController
//@PreAuthorize("permitAll()") // - if we do this things blow up on getEntityTypeName expecting an authentication object
@RequestMapping("/inbound")
@RequiredArgsConstructor
@Slf4j
@WebSlice("api")
public class InboundBaseController {

    protected final ServiceController serviceController;
    protected final FormDefinitionController formDefinitionController;
    protected final ServiceTypeController serviceTypeController;
    protected final SessionDataController sessionDataController;
    protected final IndividualRepository individualRepository;

    // see also SessionDataController
    @GetJson("/config/global")
    @ReadOnlyTransaction(timeout = 120)
    public void findSessionData(WebRequest request, HttpServletResponse response) throws IOException {
        sessionDataController.findGlobalConfig(request, response);
    }

    @GetJson("/service/{id}/")
    public ServiceViewModel findOne(@PathVariable long id, HttpServletResponse response) {
        return serviceController.findOne(id, response);
    }

    @GetJson("/servicetype/{id}")
    public ServiceTypeViewModel findOne(@PathVariable int id, HttpServletResponse response) {
        return serviceTypeController.findOne(id, response);
    }

    @GetJson(value = "/formDef/{formDefUuid}")
    public FormDefinitionViewModel findForm(@Nonnull @PathVariable UUID formDefUuid) {
        return formDefinitionController.findForm(formDefUuid);
    }

    protected static Long createContactMaybe(AgencyRepository agencyRepository, IndividualRepository individualRepository,
                                             InboundReferralResource referral, Long agencyId) {
        if (!hasText(referral.getReferrerName()) && !hasText(referral.getReferrerPhoneNumber())) {
            return null;
        }

        Individual newPerson = new Individual();
        if (agencyId != null) {
            newPerson.setCompany(agencyRepository.getOne(agencyId));
            newPerson.setArchived(java.time.LocalDate.now());
        }
        newPerson.setPhoneNumber(referral.getReferrerPhoneNumber());
        var professionalEmail = StringUtils.trimAllWhitespace(referral.getReferrerEmail());
        newPerson.setEmail(professionalEmail);
        newPerson.setJobTitle(referral.getReferrerJobTitle());
        if (hasText(referral.getReferrerName())) {
            String[] names = referral.getReferrerName().split(" ");
            newPerson.setFirstName(names[0]);
            newPerson.setLastName(names.length == 1 ? null : names[1]);
        }
        newPerson = individualRepository.save(newPerson);

        return newPerson.getId();
    }

    protected static Long createAgencyMaybe(AgencyRepository agencyRepository, @Nullable String agencyName) {
        if (!hasText(agencyName)) {
            return null;
        }

        // create one if there are no matches
        // and set immediately archived to avoid showing in lookups
        Agency agency = new Agency();
        agency.setCompanyName(agencyName);
        agency.setArchived(java.time.LocalDate.now());
        agency = agencyRepository.save(agency);
        return agency.getId();
    }

}
