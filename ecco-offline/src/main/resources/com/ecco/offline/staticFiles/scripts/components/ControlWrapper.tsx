import * as React from "react";
import {LazyExoticComponent, ReactElement, useEffect, useMemo} from "react";
import {resourceRootPath} from "application-properties"
import {useReloadHandler} from 'ecco-components';
import {DomElementContainer} from 'ecco-components-core';

export type LoadableControl = {load?: (() => void) | undefined, domElement: () => Element };

/**
 * Pass in a control instance and this will load it within a div
 *
 * ```
 *     render() {
 *         const control = useMemo(() => new ReportsListControl(), [])
 *         return <ControlWrapper control={control}/>;
 *     }
 * ```
 *
 * @deprecated Prefer useControl()
 */
function ControlWrapper<CONTROL extends LoadableControl>(props: {control: CONTROL, dontLoad?: boolean | undefined, className?: string | undefined, deps?: any[] | undefined}) {
    useEffect(() => {
        !props.dontLoad &&
            props.control.load && props.control.load();
            // Clear the content when we unmount - assumes that load renders everything
            return () => {
                props.control.domElement().innerHTML = ""; // TODO: Dangerous?? - why isn't load enough?

                // clear datepicker that get added to <body>
                const pickerEls = document.getElementsByClassName("ui-datepicker");
                for (let i = 0; i < pickerEls.length; i++) {
                    pickerEls[i].innerHTML = "";
                }
            };
        },
        props.deps || []);

    return <div className={props.className || "container-fluid v-gap-15"}>
        <link media="screen, projection" type="text/css" href={`${resourceRootPath}css/jqueryui/jqueryui.css`} rel="stylesheet"
              title="For jquery-datepicker"/>
        <DomElementContainer content={props.control.domElement()}/>
    </div>;
}

function lazyControlWrapperInner<Control extends LoadableControl, Class extends {new(...args: unknown[]): Control}>(
    factory: () => Promise<{ default: Class }>,
    className: string | undefined,
    ...args: ConstructorParameters<Class>
): LazyExoticComponent<() => ReactElement> {

    return React.lazy(() => factory()
        .then(module => {
            console.assert(module.default != undefined); // Module must have default export
            const control = new module.default(...args);
            return {default: () => <ControlWrapper control={control} className={className}/>};
        }));
}

/**
 * If you want to lazy load an ecco BaseControl the React.lazy() way, then this
 * turns an ecco BaseControl into a lazy loaded React component
 *
 * ```
 *     render() {
 *         const control = new ReportsListControl();
 *         return <ControlWrapper control={control}/>;
 *     }
 * ```
 */
export function lazyControlWrapper<Control extends LoadableControl, Class extends {new(...args: unknown[]): Control}>(
        factory: () => Promise<{ default: Class }>,
        ...args: ConstructorParameters<Class>
): LazyExoticComponent<() => ReactElement> {
    return lazyControlWrapperInner(factory, undefined, ...args);
}

export function lazyControlWrapperNoPadding<Control extends LoadableControl, Class extends {new(...args: unknown[]): Control}>(
        factory: () => Promise<{ default: Class }>,
        ...args: ConstructorParameters<Class>
): LazyExoticComponent<() => ReactElement> {
    return lazyControlWrapperInner(factory, "-dummy-", ...args);
}


export function useControl<Control extends LoadableControl, Class extends {new(...args: unknown[]): Control}>(
    controlClass: Class,
    args: ConstructorParameters<Class>,
    className?: string | undefined
): () => ReactElement {
    const control = useMemo(() => new controlClass(...args), args);
    useReloadHandler(() => control.load());
    return useMemo(() => () => <ControlWrapper control={control} className={className} deps={args}/>, [control, ...args])
}

export default ControlWrapper;