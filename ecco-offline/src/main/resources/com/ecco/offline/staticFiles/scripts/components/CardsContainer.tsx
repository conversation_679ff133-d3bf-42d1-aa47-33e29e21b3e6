import Lazy = require("lazy");
import ReferralCard = require("./ReferralCard");
import CardGroup = require("./CardGroup");
import TaskCard = require("./TaskCard");
import ServiceCard = require("../components/ServiceCard");
import * as React from "react";
import {FC} from "react";
import {dropdownList} from "ecco-components-core";
import {
    CardData,
    CardGroup as CardGroupObj,
    CardSource,
    compareCards,
    update,
    withSessionData
} from "ecco-components";
import {
    EventCard as EventCardObj,
    EventCardGroup,
    ServiceCard as ServiceCardObj,
    TaskCard as TaskCardObj,
    TaskCardGroup
} from "../calendar/cards";
import {
    ReferralCard as ReferralCardObj,
    ReferralCardGroup,
    ReferralRowCard as ReferralRowCardObj
} from "../referral/cards";
import {ReferralContactCard as ReferralContactCardObj} from "../referral/ReferralContacts";
import ReferralRowCard from "./ReferralRowCard";
import {EventCard} from "./EventCard";
import {IdNameDisabled, ReloadEvent} from "@eccosolutions/ecco-common";
import {SessionData} from "ecco-dto";
import AssociatedContactCard from "../contacts/AssociatedContactCard";
import {TargetScheduleCard, TargetScheduleCardData, TargetScheduleCardGroup} from "../dailyChecks/targetScheduleDataLoader";
import {CareOrEventCard} from "../care/CareOrEventCard";
import {Grid} from '@eccosolutions/ecco-mui';
import {CareOrEventCardData} from "../care/CareOrEventCardData";

/**
 * This component is used to:
 *  - show 'contacts' on a file (see ReferralContacts) and offline events/referrals/tasks
 *  - show on DashboardAppBar for MyEvents (nearby now -3 + 14) AdHocTasks (tasks delete, but would show 'tasks due or overdue' etc if 'adHocOnly = true' was false)
 *      and Services (menu.welcome.link.dashboardServices) rendering ServiceCard for 'SLAs overdue' per service
 *  - show a 'service' filter if 'referral.cards.filter'
 * Other uses of cards use ecco-components/cards/CardsContainer
 *  - CarePage / EventPage
 *  - DailyChecksPage - 'daily app' for staff and hasRoleDailyChecks loading a TargetScheduleSource
 */

interface Props {
    sources: CardSource[];
    canShowServices?: boolean | undefined;
    width?: number | undefined;
}

interface State {
    cards?: CardData[] | undefined;
    services?: IdNameDisabled[] | undefined;
    serviceChosenId: number;
}

/*
function filterCard(card: CardData, serviceChosenId: number) {
    if (card instanceof ReferralCardObj) {
        if (serviceChosenId && (card.dto.serviceId != serviceChosenId)) {
            return false;
        }
    }
    return true;
}
*/

function gridItemComponentFactory(card: CardData, serviceChosenId: number, sessionData: SessionData) {
    const component = componentFactory(card, serviceChosenId, sessionData);
    return card instanceof CardGroupObj
        ? component
        : <Grid item className="card" md={4} sm={6} xs={12}>{component}</Grid>;
}

// TODO: Extract into something provided by a React context so that we don't need to know about
//   all the components here, or we provide the relevant factory elements when we create <CardsContainer> with the
//   sources.
function componentFactory(card: CardData, serviceChosenId: number, sessionData: SessionData) {
    if (card instanceof ReferralCardGroup) {
        const RCG = CardGroup.of<ReferralCardObj>();
        return (<RCG key={card.key} title={card.title} group={card} serviceChosenId={serviceChosenId} sessionData={sessionData}
                     cardFactory={componentFactory} />);
    }
    else if (card instanceof ReferralCardObj) {
        const svcId = sessionData.getServiceCategorisation(card.dto.serviceAllocationId).serviceId;
        if (serviceChosenId && (svcId != serviceChosenId)) {
            return (<span/>);
        }
        return (<ReferralCard key={card.dto.referralId} referral={card.dto} />);
    }
    else if (card instanceof ReferralRowCardObj) {
        if (serviceChosenId && (sessionData.getServiceCategorisation(card.dto.serviceAllocationId).serviceId != serviceChosenId)) {
            return (<span/>);
        }
        return (<ReferralRowCard key={card.dto.serviceRecipientId} referral={card.dto} />);
    }
    else if (card instanceof EventCardObj) {
        return (<EventCard key={card.dto.uid} event={card.dto} />);
    }
    else if (card instanceof TaskCardObj) {
        return (<TaskCard key={card.dto.taskHandle} task={card.dto} />);
    }
    else if (card instanceof ServiceCardObj) {
        return (<ServiceCard key={card.dto.serviceId} summary={card.dto} />);
    }
    else if (card instanceof EventCardGroup) {
        const ECG = CardGroup.of<EventCardObj>();
        return (<ECG key={card.key} title={card.title} group={card} serviceChosenId={serviceChosenId} sessionData={sessionData}
                     cardFactory={componentFactory} />);
    }
    else if (card instanceof TaskCardGroup) {
        const TCG = CardGroup.of<TaskCardObj|ReferralRowCardObj>();
        return (<TCG key={card.key} title={card.title} group={card} serviceChosenId={serviceChosenId} sessionData={sessionData}
                          cardFactory={componentFactory} />);
    }
    else if (card instanceof ReferralContactCardObj) {
        return (<AssociatedContactCard key={card.dto.contactId} association={card.dto} printView={card.printView} />);
    }
    else if (card instanceof TargetScheduleCardGroup) {
        /*return (<TargetScheduleCard key={card.dto.referral.serviceRecipientId} schedule={card.dto} />);*/
        const CSG = CardGroup.of<TargetScheduleCardData>();
        return (<CSG key={card.key} title={card.title} group={card} serviceChosenId={serviceChosenId} sessionData={sessionData}
                     cardFactory={componentFactory}/>);
    }
    else if (card instanceof TargetScheduleCardData) {
        return (<TargetScheduleCard key={'task-schedule'.concat(card.dto.schedule.actionInstanceUuid)} schedule={card.dto} />);
    }
    else if (card instanceof CareOrEventCardData) {
        return (<CareOrEventCard key={card.dto.uid} event={card.dto} />);
    }
    throw new Error("Unrecognised type:" + Object.getPrototypeOf(card));
}

export class _CardsContainer extends React.Component<Props & {sessionData: SessionData}, State> {

    constructor(props: Props & {sessionData: SessionData}, context?: any) {
        super(props, context);
        this.state = {cards: [], services: [], serviceChosenId: null};
    }

    override UNSAFE_componentWillMount() {
        this.loadCards();
        ReloadEvent.bus.addHandler(this.loadCards);
    }

    override componentDidMount() {
        this.loadServiceCategorisations();
    }

    // we do need a removeHandler - see CardsContainer in this commit
    public override componentWillUnmount() {
        ReloadEvent.bus.removeHandler(this.loadCards);
    }

    private loadServiceCategorisations = () => {
        const svcCatAsArray: IdNameDisabled[] = Lazy(
                this.props.sessionData.getRestrictedServiceCategorisations(false))
            .uniq(svcCat => svcCat.serviceId)
            .map(sc => {
                return {id: sc.serviceId, name : sc.serviceName, disabled: false};
            }).toArray();

        this.setState({services: svcCatAsArray});
    };

    private loadCards = () => {
        this.setState({cards: []});
        this.props.sources.forEach(source => {
            source.getCards().forEach(card => {
                this.setState(state => update(state, {cards: {$push: [card]}}));
            });
        });
    };


    // TODO: render() should be based on the filterCriteria property (or state??) see pulling up state, such that
    // a re-render on search input change will cause the results to be filtered
    override render() {

        const showServices = this.props.canShowServices || false;
        let filter = showServices && this.props.sessionData && this.props.sessionData.isEnabled("referral.cards.filter") ? (
            /* not components/MUIComponentUtils - although it was nice, it was hardly visible */
            <Grid container>
                {/*TODO: extract this and pass in filter config as props so that we can use any UI for this */}
                <div className="col-xs-4 col-xs-offset-4">
                    {dropdownList("services", state => this.setState(state), this.state,
                        "serviceChosenId",
                        this.state.services, {})}
                </div>
            </Grid>
        ) : null;

        return (
            <div className="cards-container">
                {filter}
                <Grid container>
                    {this.state.cards
                        //.filter(card => filterCard(card, this.state.serviceChosenId))
                        .sort(compareCards)
                        .map(card => gridItemComponentFactory(card, this.state.serviceChosenId, this.props.sessionData))}
                </Grid>
            </div>
        );
    }
}

export const CardsContainer: FC<Props> = props =>
    withSessionData(sessionData => <_CardsContainer sources={props.sources} sessionData={sessionData}/>);
