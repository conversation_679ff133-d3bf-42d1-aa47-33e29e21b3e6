import $ = require("jquery");
import bloodhound = require("bloodhound");
import SearchInput = require("../controls/SearchInput");
import Form = require("../controls/Form");
import SearchableContainer = require("./SearchableContainer");
import SummarisedElement = require("./SummarisedElement");
import SearchableListControlOptions = require("./SearchableListControlOptions");
import Bloodhound = bloodhound.Bloodhound;
import {ResizeEvent} from "@eccosolutions/ecco-common";
import BaseControl from "./BaseControl";
import Spinner from "./Spinner";

class SearchableListControl<T> extends BaseControl {

    private searchAgent: Bloodhound<T>;
    private searchForm: Form = new Form();
    private searchableContainer: SearchableContainer;

    constructor(private containerClass: {new ():SearchableContainer},
            private options: SearchableListControlOptions<T>) {
        super($("<div>"));
        this.searchableContainer = new containerClass();
    }

    /** Must be called before load */
    public setSearchPlaceholderText(text: string) {
        this.options.placeholderText = text;
    }

    protected getParentForm(): Element {
        return null;
    }

    public reload(options: SearchableListControlOptions<T>) {
        this.options = options;
        this.load();
    }

    /** Trigger async load and render to the container */
    public load() {
        this.element()
            .empty()
            .append(new Spinner().element());
        this.options.loadInner( items => this.populateControls(items) );
    }

    private populateControls(items: T[]) {
        this.element()
            .empty()
            .addClass('searchable-list')
            .append(this.getParentForm())
            .append(this.searchForm.element())
            .append(this.searchableContainer.element());
        this.searchableContainer.element().empty();

        if (this.options.addEntryText) {
            this.searchableContainer.addControlForNewEntry(this.options.addEntryText, this.options.addEntryIconClasses,
                this.options.addEntryIsAdminOnly, this.options.addNewEntry);
        }

        items.forEach(item => {
            this.addControlFor(item);
        });
        this.initSearch(items);
    }

    private addControlFor(item: T) {
        const control = this.options.createControl(item);
        this.render(control);
    }

    private initSearch(items: T[]): void {
        this.searchAgent = new Bloodhound(this.options.generateSearchConfig(items));
        this.searchAgent.initialize();

        const searchBox: SearchInput = new SearchInput(this.options.placeholderText);
        this.searchForm.element().empty();
        this.searchForm.append(searchBox.element()); // NOTE: Not JQuery's append, so don't chain from .empty()
        searchBox.change((val: string) => {
            this.options.onChange && this.options.onChange(val);
            this.searchFor(val)
        }, true, true);
    }

    public select(item: T) {
        this.searchableContainer.hideAll();
        this.searchableContainer.show(this.options.generateKey(item));
        this.searchableContainer.addClass("selected", this.options.generateKey(item));
    }

    public searchFor(val: string) {
        this.searchableContainer.removeClass("selected");
        if (val) {
            this.searchAgent.get(val, (items: T[]) => {
                this.searchableContainer.hideAll();
                items.forEach(item => {
                    const key = this.options.generateKey(item);
                    if (this.searchableContainer.hasItem(key)) {
                        this.searchableContainer.show(key);
                    }
                    else {
                        this.addControlFor(item);
                    }
                })
            });
        } else {
            this.searchableContainer.showAll();
        }
        ResizeEvent.bus.fire();

    }

    private render(control: SummarisedElement<T>): void {
        this.searchableContainer.append(control.searchId(), control.title(), control.body(), control.initiallyHidden());
    }
}
export = SearchableListControl;