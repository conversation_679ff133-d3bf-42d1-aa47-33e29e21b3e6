import {EccoDateTime, Result} from "@eccosolutions/ecco-common";
import {ReferralSummaryWithEntities, ReferralWithEntities, ServiceRecipientWithEntities} from "../dto";
import {BaseServiceRecipientCommandDto, DeleteEvidenceRequestCommandDto, TaskStatus} from "../evidence-dto";
import {
    DeleteRequestServiceRecipientCommandDto,
    ServiceRecipientTaskBaseCommandDto as ReferralTaskBaseCommand
} from "../evidence/evidence-command-dto";
import {
    ReferralDto,
    ReferralsListRow,
    ReferralSummaryDto as ReferralSummaryDto,
    RelatedRelationship,
    ServiceRecipientAssociatedContact
} from "../referral-dto";
import {ReportCriteriaDto} from '../reports/ReportCriteriaDto';
import {ConfigResolverDefault, Service} from "../service-config-domain";
import {ServiceRecipient} from "../service-recipient-dto";
import {SessionDataAjaxRepository} from "../session-data/SessionDataAjaxRepository";
import {ReferralRepository} from "./ReferralRepository";
import {ApiClient, ResourceList} from "../web-api";


export class ReferralAjaxRepository implements ReferralRepository {
    private sessionDataAjaxRepository = new SessionDataAjaxRepository(this.apiClient);

    constructor(private apiClient: ApiClient) {}

    public findOneReferral(referralId: number): Promise<ReferralDto> {
        return this.apiClient.get<ReferralDto>(`referrals/${referralId}/`);
    }

    public findOneReferralSummary(referralId: number): Promise<ReferralDto> {
        return this.apiClient.get<ReferralDto>(`referrals/${referralId}/summary/`);
    }

    public findOneReferralSummaryAsSummary(referralId: number): Promise<ReferralSummaryDto> {
        return this.findOneReferralSummary(referralId) as Promise<ReferralSummaryDto>;
    }

    // NB unused - consider a 'latest' type query instead?
    public findTaskStatusByServiceRecipientId(serviceRecipientId: number): Promise<TaskStatus[]> {
        return this.apiClient.get<TaskStatus[]>(
            `taskStatus/byServiceRecipient/${serviceRecipientId}/`
        );
    }

    public findOneReferralSummaryByServiceRecipientIdAsReferral(
        serviceRecipientId: number
    ): Promise<ReferralDto> {
        return this.apiClient.get<ReferralDto>(
            `referrals/byServiceRecipient/${serviceRecipientId}/summaryAsReferral/`
        );
    }

    /**
     * Explicitly uses the transformation of ReferralSummaryViewModel to get the ReferralSummary.
     * This is so we can get the AcceptState - which could possibly be done client-side upon receiving the referral.
     * The server used to return ReferralViewModel for mapping to client side Referral, but this was transferred
     * to ReferralSummaryViewModel in gitish 7956501.
     */
    public findOneReferralSummaryByServiceRecipientIdUsingDto(
        serviceRecipientId: number
    ): Promise<ReferralSummaryDto> {
        return this.apiClient.get<ReferralSummaryDto>(
            `referrals/byServiceRecipient/${serviceRecipientId}/summaryAsReferral/`
        );
    }

    /**
     * Used by reports to get related referral information to, say, reviews
     * Now obtains from ReferralSummaryViewModel, not ReferralSummary directly
     */
    public findAllReferralSummaryByServiceRecipientId(
        serviceRecipientIds: number[] | string[]
    ): Promise<ReferralSummaryDto[]> {
        return this.apiClient.get<ReferralSummaryDto[]>("referrals/byServiceRecipients/summary/", {
            query: {ids: serviceRecipientIds.join(",")}
        });
    }

    /**
     * Used by group support.
     * Now obtains from ReferralSummaryViewModel, not ReferralSummary directly
     */
    public findAllUnsecuredAclReferralSummaryByServiceRecipientId(
        serviceRecipientIds: number[] | string[]
    ): Promise<ReferralSummaryDto[]> {
        return this.apiClient.get<ReferralSummaryDto[]>(
            "referrals/byServiceRecipients/summaryNoAcl/",
            {
                query: {ids: serviceRecipientIds.join(",")}
            }
        );
    }

    /**
     * Ad ReportRepository.findAllReferralSummary, but access through a 'staff' path (not 'reports')
     * @param reportDto
     * @param page
     */
    public findAllReferralSummary(
        reportDto: ReportCriteriaDto | Promise<ReportCriteriaDto>
    ): Promise<ReferralDto[]> {
        let path = "referrals/summary/";
        return Promise.resolve(reportDto).then(criteria =>
            this.apiClient.postWithReAuth<ReferralDto[]>(path, criteria)
        );
    }

    /**
     * ReferralSummary coerced back into Referral.
     */
    public findAllReferralWithoutSecuritySummaryByClientAsReferral(
        clientId: number
    ): Promise<ReferralDto[]> {
        return this.findAllReferralWithoutSecuritySummaryByClient(clientId).then(summaries =>
            summaries.map(sum => sum as ReferralDto)
        );
    }
    /**
     * Return a list of referrals for this client.
     * SECURITY WARNING! That this will include ALL regardless, but have a _readOnly property set for those without permission
     * Explicitly uses the transformation of ReferralSummaryViewModel to get the ReferralSummary.
     */
    // TODO rename to findAllReferralWithoutAclSecuritySummaryByClient
    public findAllReferralWithoutSecuritySummaryByClient(
        clientId: number
    ): Promise<ReferralSummaryDto[]> {
        return this.apiClient.get<ReferralSummaryDto[]>(`clients/${clientId}/referrals/summary/`);
    }

    public findOneReferralByCode(referralCode: string): Promise<ReferralDto> {
        return this.apiClient.get<ReferralDto>(`referrals/byCode/${referralCode}/`);
    }

    public findOneReferralByServiceRecipientId(serviceRecipientId: number): Promise<ReferralDto> {
        return this.apiClient.get<ReferralDto>(
            `referrals/byServiceRecipient/${serviceRecipientId}/`
        );
    }

    public findServiceRecipientIdFromReferralId(referralId: number): Promise<number> {
        return this.apiClient.get<number>(`referrals/${referralId}/serviceRecipientId/`);
    }

    public findAssociatedContactsByServiceRecipientId(
        serviceRecipientId: number
    ): Promise<ServiceRecipientAssociatedContact[]> {
        return this.apiClient.get<ServiceRecipientAssociatedContact[]>(
            `servicerecipient/${serviceRecipientId}/contacts/`
        );
    }

    public findOneServiceRecipientWithEntities(
        serviceRecipientId: number
    ): Promise<ServiceRecipientWithEntities> {
        return this.internalFindOneWithEntitiesWithEntities(serviceRecipientId, id =>
            this.findOneServiceRecipientById(id)
        );
    }
    private findOneServiceRecipientById(serviceRecipientId: number): Promise<ServiceRecipient> {
        return this.apiClient.get<ServiceRecipient>(`service-recipient/${serviceRecipientId}/`);
    }

    public findOneReferralSummaryWithEntitiesUsingDto(
        serviceRecipientId: number
    ): Promise<ReferralSummaryWithEntities> {
        return this.internalFindOneWithEntitiesWithEntities(serviceRecipientId, id =>
            this.findOneReferralSummaryByServiceRecipientIdUsingDto(id)
        );
    }

    public findOneReferralWithEntities(serviceRecipientId: number): Promise<ReferralWithEntities> {
        return this.internalFindOneWithEntitiesWithEntities(serviceRecipientId, id =>
            this.findOneReferralByServiceRecipientId(id)
        );
    }

    private internalFindOneWithEntitiesWithEntities<SR extends ServiceRecipientWithEntities>(
        serviceRecipientId: number,
        fetcher: (id: number) => Promise<ServiceRecipient>
    ): Promise<SR> {
        const serviceRecipientQ = fetcher(serviceRecipientId);
        const featuresQ = this.sessionDataAjaxRepository.getSessionData();
        return Promise.all([serviceRecipientQ, featuresQ]).then(([recipient, features]) => {
            const result = recipient as SR; // FIXME: This is an unsafe cast.
            result.features = features;
            result.configResolver = ConfigResolverDefault.fromServiceRecipient(
                features,
                recipient.serviceAllocationId,
                recipient.serviceTypeId
            );
            return Promise.resolve(result);
        });
    }

    public findAllReferralsByParentServiceRecipientId(
        parentServiceRecipientId: number
    ): Promise<ReferralDto[]> {
        return this.apiClient.get<ReferralDto[]>(
            `service-recipients/${parentServiceRecipientId}/children/`
        );
    }

    /**
     * This impl isn't used currently invoked since offline sync calls SecureReferralAjaxRepository to obtain a list
     * for referrals offline which is put in the device database for later use by OfflineRepository.
     * However, we want to retain the ability for offline/online being consistent so we implement it.
     */
    public findAllReferralsForOffline(): Promise<ReferralDto[]> {
        // FIXME: getCached is working around some messy stuff where we're requesting per CardGroup entry in rapid succession (getCached is 3 sec TTL)
        return this.apiClient.getCached<ReferralDto[]>("user/_self/referrals/filter/live/");
    }

    public findAllReferrals(
        serviceId: number,
        projectId?: number | undefined,
        statusGroup?: string | undefined,
        page?: number | undefined,
        pageSize?: number | undefined
    ): Promise<ResourceList<ReferralsListRow>> {
        const path = "referrals/list/";
        const query = {
            statusGroup: statusGroup || "default",
            service: undefined as string | undefined,
            project: undefined as string | undefined,
            page: undefined as string | undefined,
            pageSize: undefined as string | undefined
        };

        if (serviceId != null) {
            query.service = serviceId.toString();
        }

        if (projectId != null) {
            query.project = projectId.toString();
        }

        if (page != null) {
            query.page = page.toString();
        }

        if (pageSize != null) {
            query.pageSize = pageSize.toString();
        }

        return this.apiClient.get<ResourceList<ReferralsListRow>>(path, {query});
    }

    public findAllReferralsInsideBuilding(buildingId: number): Promise<ReferralDto[]> {
        return this.apiClient.get<ReferralDto[]>(`buildings/${buildingId}/referrals/`);
    }

    public findRelatedReferrals(referralId: number): Promise<RelatedRelationship[]> {
        return this.apiClient.get<RelatedRelationship[]>(`referrals/${referralId}/related/`);
    }

    public findRelatedReferralsToPrimary(
        primaryReferralId: number
    ): Promise<RelatedRelationship[]> {
        return this.apiClient.get<RelatedRelationship[]>(
            `referrals/primary/${primaryReferralId}/related/`
        );
    }

    public findUnfilteredReferralsByClientName(
        firstName: string,
        lastName: string
    ): Promise<ReferralDto[]> {
        return this.apiClient.post<ReferralDto[]>("referrals/unfiltered/byClient/query/", {
            lastName: lastName,
            firstName: firstName
        });
    }

    /**
     * Clone of CommandAjaxRepository.findOne.
     * Here to avoid adding to the CommandRepository interface which causes more pain.
     */
    findOneCommand(uuid: string, optional = false): Promise<BaseServiceRecipientCommandDto> {
        const apiPath = `service-recipients/command/${uuid}/`;
        const options = !optional ? undefined : {query: {optional: "true"}};
        return this.apiClient.get<BaseServiceRecipientCommandDto>(apiPath, options);
    }

    public findLatestServiceRecipientDeleteRequest(
        serviceRecipientId: number
    ): Promise<DeleteRequestServiceRecipientCommandDto | null> {
        let path = `service-recipients/${serviceRecipientId}/commands/delete-request/`;
        return this.apiClient
            .get<DeleteRequestServiceRecipientCommandDto[]>(path)
            .then(requests => {
                if (!requests.length) {
                    return null;
                }
                return requests.reduce((prev, curr) =>
                    EccoDateTime.parseIso8601Utc(prev.timestamp)!!.laterThan(
                        EccoDateTime.parseIso8601Utc(curr.timestamp)!!
                    )
                        ? prev
                        : curr
                );
            });
    }

    public findLatestEvidenceDeleteRequestForWorkUuid(
        serviceRecipientId: number,
        workUuid: string
    ): Promise<DeleteEvidenceRequestCommandDto | null> {
        let path = `service-recipients/${serviceRecipientId}/evidence/commands/delete-request/`;
        return this.apiClient.get<DeleteEvidenceRequestCommandDto[]>(path).then(requests => {
            if (!requests.length) {
                return null;
            }
            const matchWork = requests.filter(cmd => cmd.workUuid == workUuid);
            return matchWork.length == 0
                ? null
                : matchWork.reduce((prev, curr) =>
                      EccoDateTime.parseIso8601Utc(prev.timestamp)!!.laterThan(
                          EccoDateTime.parseIso8601Utc(curr.timestamp)!!
                      )
                          ? prev
                          : curr
                  );
        });
    }

    /** Direct ReferralSummary - does NOT use ReferralSummaryViewModel */
    public findAllHiddenReferrals(): Promise<ReferralSummaryDto[]> {
        return this.apiClient.get<ReferralSummaryDto[]>("hidden/referrals/");
    }

    public findLatestCommandPerTaskName(
        serviceRecipientId: number
    ): Promise<ReferralTaskBaseCommand[]> {
        return this.apiClient.get<ReferralTaskBaseCommand[]>(
            `service-recipients/${serviceRecipientId}/commands/tasks/latest/`
        );
    }

    public deleteByIdWithClientId(referralId: number, clientId: number) {
        return this.apiClient.del(`referrals/${referralId}/`, {clientId: clientId});
    }

    public moveReferralToClient(referralId: number, clientId: number) {
        return this.apiClient.post<void>(`move/referrals/${referralId}/client/${clientId}/`, {});
    }

    public moveReferralToService(referralId: number, serviceId: number) {
        return this.apiClient.post<void>(`move/referrals/${referralId}/service/${serviceId}/`, {});
    }

    public hideReferral(referral: {referralId: number}): Promise<void> {
        return this.apiClient.post<void>("hidden/referrals/", referral);
    }

    public unhideReferral(referral: {referralId: number}): Promise<void> {
        return this.apiClient.del<void>(`hidden/referrals/${referral.referralId}/`, "");
    }

    public static createReferralIdExtractor(result: Result): number {
        // see comments on ReferralController.EXTRACT_ID_FN
        const href = result.links[0]!.href;
        const matches = /referrals\/(\d+)\//.exec(href);
        return Number(matches!![1]);
    }
}
