package com.ecco.evidence.repositories;

import com.ecco.dao.EvidenceThreatActionSummary;
import com.ecco.dom.EvidenceThreatAction;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.jspecify.annotations.NonNull;

import com.ecco.evidence.dom.AssociatedAction;
import org.joda.time.DateTime;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;

public interface ThreatActionRepository extends Repository<EvidenceThreatAction, Long> {

    @Query("select new com.ecco.dao.EvidenceThreatActionSummary(" +
            " a.id, c.name, a.goalName, a.goalPlan, a.actionInstanceUuid," +
            " a.parentActionInstanceUuid, a.hierarchy, a.position," +
            " c.id, r.id, o.id, a.status, a.statusChange, a.target, a.expiryDate, a.work.id, a.work.workDate," +
            " a.likelihood, a.severity, a.hazard, a.intervention, a.score)" +
            " from EvidenceThreatAction a left join a.action c left join c.actionGroup r left join r.outcome o" +
            " where a.work.serviceRecipient.id in ?1" +
            " ORDER BY a.work.workDate DESC, a.work.created DESC")
    List<EvidenceThreatActionSummary> findAllThreatActionSummaryByServiceRecipientIds(Set<Integer> serviceRecipientIds);

    /**
     * DEPRECATED - delete it, in favour of finding the whole snapshot at a point in time using existing code (then filter).
     * This is because this query finds the latest 'created', just as the below deprecated method finds the latest id', but these only works
     * because at the moment we only ever allow users to see the latest snapshot - but when it comes to editing, this won't work.
     */
    @NonNull
    @Query("SELECT action FROM EvidenceThreatAction action " +
            "WHERE action.serviceRecipient.id = ?1 " +
            "    AND action.actionInstanceUuid = ?2 " +
            "    AND action.created <= ?3 " +
            "ORDER BY action.created DESC")
    @Deprecated
    List<EvidenceThreatAction> findLatestByServiceRecipientIdAndActionInstanceUuidAndCreatedLessOrEqualTo(
            int serviceRecipientId, UUID actionInstanceUuid,
            @NonNull DateTime timestamp, @NonNull Pageable pageable);


    /**
     * DEPRECATED - delete it, but still in legacy code
     * Get the latest command for a referralId and taskName.
     * An attempt to mimic http://stackoverflow.com/questions/2751941/hibernate-query-get-latest-versions-by-timestamp
     * @see WhereExpressionVisitorHql.visit(GroupCriteria e)
     */
    @Deprecated
    @Query("SELECT a1 FROM EvidenceThreatAction a1 WHERE " +
            "a1.serviceRecipientId = ?1 AND " +
            "a1.id >= all ( " +
            "SELECT a2.id FROM EvidenceThreatAction a2 WHERE "+
            "a2.serviceRecipientId = ?1 AND " +
            "a2.actionInstanceUuid = a1.actionInstanceUuid AND " +
            "a2.action.id = a1.action.id)")
    List<EvidenceThreatAction> findLatestActionsByServiceRecipientId(Integer serviceRecipientId); // TODO: Rename to findLatestActionInstancesBy...

    @Query("SELECT new com.ecco.evidence.dom.AssociatedAction(a.id, w.id)" +
            " FROM EvidenceThreatWork w JOIN w.associatedActions a" +
            " WHERE w.serviceRecipient.id in ?1")
    List<AssociatedAction> findAllAssociatedActionsByWork_serviceRecipientIds(Set<Integer> serviceRecipientIds);

    @NonNull
    EvidenceThreatAction save(@NonNull EvidenceThreatAction action);
}
