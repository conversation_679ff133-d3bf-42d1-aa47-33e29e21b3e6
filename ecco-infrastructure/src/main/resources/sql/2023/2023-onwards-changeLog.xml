<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- recreate each year so its included in each run -->
    <changeSet id="should-never-execute" context="should_never_execute" author="nealeu">
        <preConditions onFail="HALT">
            <tableExists tableName="never_exists"/>
        </preConditions>
        <comment>Deliberate changeset to detect if all contexts are being run somehow</comment>
    </changeSet>


    <!-- ALWAYS ensure we have executed the final one of previous year first -->
    <changeSet id="fail-if-not-run-pre2023" author="adamjhamer">
        <preConditions onFail="HALT">
            <changeSetExecuted
                    id="DEV-2418-referralaspects-add-customFormsMgr"
                    author="adamjhamer"
                    changeLogFile="2022/general-domain"/>
        </preConditions>
        <comment>A no-op changeset that HALTs if we've not run earlier changes</comment>
    </changeSet>


    <!-- Add domains in order of their dependency on each other -->
    <include file="classpath:sql/2023/config-domain/001-configDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/cosmo-domain/001-cosmoDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/security-domain/001-securityDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/hr-domain/001-hrDomainChangeLog.xml"/>
    <!-- NB also create buildings-domain? -->
    <include file="classpath:sql/2023/evidence-domain/001-evidenceDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/finance-domain/001-financeDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/general-domain/001-generalDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/general-domain/002-report-defs.xml"/>
    <!-- moved after the rename of servicetypes_taskdefinitions in 001-generalDomainChangeLog.xml -->
    <!-- however, the rename should ideally be part of the evidence domain -->
    <include file="classpath:sql/2023/evidence-domain/002-groupsupport.xml"/>
    <include file="classpath:sql/2023/incidents-domain/001-incidentsDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/buildings-domain/001-repairsDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/buildings-domain/004-managedVoidsDomainChangeLog.xml"/>

    <include file="classpath:sql/2024/2024-onwards-changeLog.xml"/>
    <!-- *** STOP:
     DO NOT ADD ANYTHING MORE HERE
     - USE A CHANGELOG
     in the correct YEAR folder
     *** -->

</databaseChangeLog>