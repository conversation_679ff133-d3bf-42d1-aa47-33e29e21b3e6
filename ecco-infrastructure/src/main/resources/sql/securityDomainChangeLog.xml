<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <!-- maintains a separate isolated domain -->

    <!-- HANDLES: (based on search for <createTable)
     - hibernate_sequences, userdevices, userdevices_AUD, commandqueue, acl_sid, acl_class,
     - acl_object_identity, acl_entry, passwordhistory, passwordhistory_AUD,
     - group_members
     - usr_commands
     -
     - NB any changesets that rely on previous changesets in the general domain will need moving to the general domain
     - or carefully separating into its own file, because of the order specified oin 1.2-inwards-changelog.xml
    -->

    <include file="classpath:sql/2020-and-earlier-from-1.1/securityDomainChangeLog.xml"/>
    <include file="classpath:sql/2020-and-earlier-from-1.1/securityDomainChangeLogPost.xml"/>
    <include file="classpath:sql/2021/security-domain/001-securityDomainChangeLog.xml"/>
    <include file="classpath:sql/2022/security-domain/001-securityDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/security-domain/001-securityDomainChangeLog.xml"/>
    <include file="classpath:sql/2024/security-domain/001-securityDomainChangeLog.xml"/>

</databaseChangeLog>
