<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
        logicalFilePath="2024/security-domain">

    <!-- HANDLES: (based on search for <createTable)
     - see main securityDomainChangeLog.xml for what tables are involved in the domain
    -->

    <changeSet id="DEV-2673-magic-link" author="djc">
        <createTable tableName="sec_magictoken">
            <column name="credentials" type="CHAR(22)">
                <constraints nullable="false"
                             primaryKey="true"
                             primaryKeyName="pk_sec_magictoken"/>
            </column>
            <column name="expiry" type="DATETIME"/>
            <column name="revoked" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="target_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="target_path" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint baseColumnNames="target_user_id"
                                 baseTableName="sec_magictoken"
                                 constraintName="fk_magictoken_user"
                                 referencedColumnNames="id"
                                 referencedTableName="users"/>
    </changeSet>

</databaseChangeLog>