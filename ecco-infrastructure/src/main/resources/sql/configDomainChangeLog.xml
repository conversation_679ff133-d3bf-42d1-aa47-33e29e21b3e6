<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <!-- maintains a separate isolated domain -->

    <!-- HANDLES: (based on search for <createTable)
     - hibernate_sequences, messages, setting, uploadbytes, uploadfile,
     - templates, cfg_module (was softwaremodule), cfg_feature, cfg_externalsystem, cfg_list_definitions,
     - cfg_commands, cfg_form_definitions, menu, menu_menuitem, menuitem
     -
     - NB any changesets that rely on previous changesets in the general domain will need moving to the general domain
     - or carefully separating into its own file, because of the order specified oin 1.2-inwards-changelog.xml
    -->

    <include file="classpath:sql/2020-and-earlier-from-1.1/configDomainChangeLog.xml"/>
    <include file="classpath:sql/2021/config-domain/001-configDomainChangeLog.xml"/>
    <include file="classpath:sql/2022/config-domain/001-configDomainChangeLog.xml"/>
    <include file="classpath:sql/2023/config-domain/001-configDomainChangeLog.xml"/>
    <include file="classpath:sql/2024/config-domain/001-configDomainChangeLog.xml"/>

</databaseChangeLog>
