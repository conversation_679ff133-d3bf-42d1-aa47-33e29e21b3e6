package com.ecco.webApi.boot;

import com.ecco.infrastructure.web.WebSlice.WebSliceMatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration(proxyBeanMethods = false)
public class HttpRemapConfig implements WebMvcConfigurer {

    @Value("${ecco.api.basePath}") // see application.properties
    private String apiBasePath;

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.addPathPrefix(apiBasePath, new WebSliceMatch("api"));
        configurer.addPathPrefix("/ui", new WebSliceMatch("ui"));
        configurer.addPathPrefix("/nav", new WebSliceMatch("nav"));
    }

}
